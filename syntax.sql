CREATE OR REPLACE ROW ACCESS POLICY store_region_policy
AS (region STRING) RETURNS BOOLEAN ->
    CASE
        WHEN CURRENT_ROLE() = 'HRZN_CARIBBEAN_HEAD'        THEN region = 'Caribbean'
        WHEN CURRENT_ROLE() = 'HRZN_CENTRAL_AFRICA_HEAD'   THEN region = 'Central Africa'
        WHEN CURRENT_ROLE() = 'HRZN_CENTRAL_AMERICA_HEAD'  THEN region = 'Central America'
        WHEN CURRENT_ROLE() = 'HRZN_CENTRAL_ASIA_HEAD'     THEN region = 'Central Asia'
        WHEN CURRENT_ROLE() = 'HRZN_CENTRAL_US_HEAD'       THEN region = 'Central US'
        WHEN CURRENT_ROLE() = 'HRZN_EASTERN_AFRICA_HEAD'   THEN region = 'Eastern Africa'
        WHEN CURRENT_ROLE() = 'HRZN_EASTERN_ASIA_HEAD'     THEN region = 'Eastern Asia'
        WHEN CURRENT_ROLE() = 'HRZN_EASTERN_CANADA_HEAD'   THEN region = 'Eastern Canada'
        WHEN CURRENT_ROLE() = 'HRZN_EASTERN_EUROPE_HEAD'   THEN region = 'Eastern Europe'
        WHEN CURRENT_ROLE() = 'HRZN_EASTERN_US_HEAD'       THEN region = 'Eastern US'
        WHEN CURRENT_ROLE() = 'HRZN_NORTH_AFRICA_HEAD'     THEN region = 'North Africa'
        WHEN CURRENT_ROLE() = 'HRZN_NORTHERN_EUROPE_HEAD'  THEN region = 'Northern Europe'
        WHEN CURRENT_ROLE() = 'HRZN_OCEANIA_HEAD'          THEN region = 'Oceania'
        WHEN CURRENT_ROLE() = 'HRZN_SOUTH_AMERICA_HEAD'    THEN region = 'South America'
        WHEN CURRENT_ROLE() = 'HRZN_SOUTHEASTERN_ASIA_HEAD' THEN region = 'Southeastern Asia'
        WHEN CURRENT_ROLE() = 'HRZN_SOUTHERN_AFRICA_HEAD'  THEN region = 'Southern Africa'
        WHEN CURRENT_ROLE() = 'HRZN_SOUTHERN_ASIA_HEAD'    THEN region = 'Southern Asia'
        WHEN CURRENT_ROLE() = 'HRZN_SOUTHERN_EUROPE_HEAD'  THEN region = 'Southern Europe'
        WHEN CURRENT_ROLE() = 'HRZN_SOUTHERN_US_HEAD'      THEN region = 'Southern US'
        WHEN CURRENT_ROLE() = 'HRZN_WESTERN_AFRICA_HEAD'   THEN region = 'Western Africa'
        WHEN CURRENT_ROLE() = 'HRZN_WESTERN_ASIA_HEAD'     THEN region = 'Western Asia'
        WHEN CURRENT_ROLE() = 'HRZN_WESTERN_CANADA_HEAD'   THEN region = 'Western Canada'
        WHEN CURRENT_ROLE() = 'HRZN_WESTERN_EUROPE_HEAD'   THEN region = 'Western Europe'
        WHEN CURRENT_ROLE() = 'HRZN_WESTERN_US_HEAD'       THEN region = 'Western US'
        ELSE FALSE
    END;

-- Attach Row Access Policy to the table
ALTER TABLE HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES
ADD ROW ACCESS POLICY store_region_policy ON (region);



-- Attach Row Access Policy to the table
ALTER TABLE HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.RETURN
ADD ROW ACCESS POLICY store_region_policy ON (region);


-- Attach Row Access Policy to the table
ALTER TABLE HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.REPRESENTATIVES
ADD ROW ACCESS POLICY store_region_policy ON (region);

-- Step 2: Create region-specific roles

-- Step 1: Use a high-privileged role
USE ROLE SECURITYADMIN;


-- Region-specific roles
CREATE OR REPLACE ROLE HRZN_CARIBBEAN_HEAD;
CREATE OR REPLACE ROLE HRZN_CENTRAL_AFRICA_HEAD;
CREATE OR REPLACE ROLE HRZN_CENTRAL_AMERICA_HEAD;
CREATE OR REPLACE ROLE HRZN_CENTRAL_ASIA_HEAD;
CREATE OR REPLACE ROLE HRZN_CENTRAL_US_HEAD;
CREATE OR REPLACE ROLE HRZN_EASTERN_AFRICA_HEAD;
CREATE OR REPLACE ROLE HRZN_EASTERN_ASIA_HEAD;
CREATE OR REPLACE ROLE HRZN_EASTERN_CANADA_HEAD;
CREATE OR REPLACE ROLE HRZN_EASTERN_EUROPE_HEAD;
CREATE OR REPLACE ROLE HRZN_EASTERN_US_HEAD;
CREATE OR REPLACE ROLE HRZN_NORTH_AFRICA_HEAD;
CREATE OR REPLACE ROLE HRZN_NORTHERN_EUROPE_HEAD;
CREATE OR REPLACE ROLE HRZN_OCEANIA_HEAD;
CREATE OR REPLACE ROLE HRZN_SOUTH_AMERICA_HEAD;
CREATE OR REPLACE ROLE HRZN_SOUTHEASTERN_ASIA_HEAD;
CREATE OR REPLACE ROLE HRZN_SOUTHERN_AFRICA_HEAD;
CREATE OR REPLACE ROLE HRZN_SOUTHERN_ASIA_HEAD;
CREATE OR REPLACE ROLE HRZN_SOUTHERN_EUROPE_HEAD;
CREATE OR REPLACE ROLE HRZN_SOUTHERN_US_HEAD;
CREATE OR REPLACE ROLE HRZN_WESTERN_AFRICA_HEAD;
CREATE OR REPLACE ROLE HRZN_WESTERN_ASIA_HEAD;
CREATE OR REPLACE ROLE HRZN_WESTERN_CANADA_HEAD;
CREATE OR REPLACE ROLE HRZN_WESTERN_EUROPE_HEAD;
CREATE OR REPLACE ROLE HRZN_WESTERN_US_HEAD;

GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_CARIBBEAN_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_CENTRAL_AFRICA_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_CENTRAL_AMERICA_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_CENTRAL_ASIA_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_CENTRAL_US_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_EASTERN_AFRICA_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_EASTERN_ASIA_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_EASTERN_CANADA_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_EASTERN_EUROPE_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_EASTERN_US_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_NORTH_AFRICA_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_NORTHERN_EUROPE_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_OCEANIA_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_SOUTH_AMERICA_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_SOUTHEASTERN_ASIA_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_SOUTHERN_AFRICA_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_SOUTHERN_ASIA_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_SOUTHERN_EUROPE_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_SOUTHERN_US_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_WESTERN_AFRICA_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_WESTERN_ASIA_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_WESTERN_CANADA_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_WESTERN_EUROPE_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_WESTERN_US_HEAD;


CREATE USER western_us PASSWORD='Test@123' DEFAULT_ROLE=ACCOUNTADMIN;
SELECT CURRENT_USER();

GRANT ROLE HRZN_WESTERN_US_HEAD TO USER western_us ;
-- Test as ROLE_EAST
USE ROLE HRZN_WESTERN_US_HEAD;
SELECT * FROM HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.sales;
-----------------------------------------------------------------------------------------

-- ===========================================================
-- Step 0: Use SECURITYADMIN (or ACCOUNTADMIN) for setup
-- ===========================================================
USE ROLE SECURITYADMIN;

-- ===========================================================
-- Step 1: Create roles
-- ===========================================================
CREATE OR REPLACE ROLE HRZN_GLOBAL_HEAD;

CREATE OR REPLACE ROLE HRZN_CARIBBEAN_HEAD;
CREATE OR REPLACE ROLE HRZN_CENTRAL_AFRICA_HEAD;
CREATE OR REPLACE ROLE HRZN_CENTRAL_AMERICA_HEAD;
CREATE OR REPLACE ROLE HRZN_CENTRAL_ASIA_HEAD;
CREATE OR REPLACE ROLE HRZN_CENTRAL_US_HEAD;
CREATE OR REPLACE ROLE HRZN_EASTERN_AFRICA_HEAD;
CREATE OR REPLACE ROLE HRZN_EASTERN_ASIA_HEAD;
CREATE OR REPLACE ROLE HRZN_EASTERN_CANADA_HEAD;
CREATE OR REPLACE ROLE HRZN_EASTERN_EUROPE_HEAD;
CREATE OR REPLACE ROLE HRZN_EASTERN_US_HEAD;
CREATE OR REPLACE ROLE HRZN_NORTH_AFRICA_HEAD;
CREATE OR REPLACE ROLE HRZN_NORTHERN_EUROPE_HEAD;
CREATE OR REPLACE ROLE HRZN_OCEANIA_HEAD;
CREATE OR REPLACE ROLE HRZN_SOUTH_AMERICA_HEAD;
CREATE OR REPLACE ROLE HRZN_SOUTHEASTERN_ASIA_HEAD;
CREATE OR REPLACE ROLE HRZN_SOUTHERN_AFRICA_HEAD;
CREATE OR REPLACE ROLE HRZN_SOUTHERN_ASIA_HEAD;
CREATE OR REPLACE ROLE HRZN_SOUTHERN_EUROPE_HEAD;
CREATE OR REPLACE ROLE HRZN_SOUTHERN_US_HEAD;
CREATE OR REPLACE ROLE HRZN_WESTERN_AFRICA_HEAD;
CREATE OR REPLACE ROLE HRZN_WESTERN_ASIA_HEAD;
CREATE OR REPLACE ROLE HRZN_WESTERN_CANADA_HEAD;
CREATE OR REPLACE ROLE HRZN_WESTERN_EUROPE_HEAD;
CREATE OR REPLACE ROLE HRZN_WESTERN_US_HEAD;

-- ===========================================================
-- Step 2: Grant warehouse, DB, schema, and table access
-- ===========================================================
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_WESTERN_US_HEAD;

GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_WESTERN_US_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_WESTERN_US_HEAD;

GRANT SELECT ON TABLE HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES TO ROLE HRZN_WESTERN_US_HEAD;

-- Loop over each region role
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_CARIBBEAN_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_CENTRAL_AFRICA_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_CENTRAL_AMERICA_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_CENTRAL_ASIA_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_CENTRAL_US_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_EASTERN_AFRICA_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_EASTERN_ASIA_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_EASTERN_CANADA_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_EASTERN_EUROPE_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_EASTERN_US_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_NORTH_AFRICA_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_NORTHERN_EUROPE_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_OCEANIA_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_SOUTH_AMERICA_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_SOUTHEASTERN_ASIA_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_SOUTHERN_AFRICA_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_SOUTHERN_ASIA_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_SOUTHERN_EUROPE_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_SOUTHERN_US_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_WESTERN_AFRICA_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_WESTERN_ASIA_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_WESTERN_CANADA_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_WESTERN_EUROPE_HEAD;
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_WESTERN_US_HEAD;

-- Database & schema
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_CARIBBEAN_HEAD;
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_CENTRAL_AFRICA_HEAD;
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_CENTRAL_AMERICA_HEAD;
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_CENTRAL_ASIA_HEAD;
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_CENTRAL_US_HEAD;
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_EASTERN_AFRICA_HEAD;
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_EASTERN_ASIA_HEAD;
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_EASTERN_CANADA_HEAD;
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_EASTERN_EUROPE_HEAD;
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_EASTERN_US_HEAD;
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_NORTH_AFRICA_HEAD;
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_NORTHERN_EUROPE_HEAD;
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_OCEANIA_HEAD;
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_SOUTH_AMERICA_HEAD;
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_SOUTHEASTERN_ASIA_HEAD;
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_SOUTHERN_AFRICA_HEAD;
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_SOUTHERN_ASIA_HEAD;
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_SOUTHERN_EUROPE_HEAD;
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_SOUTHERN_US_HEAD;
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_WESTERN_AFRICA_HEAD;
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_WESTERN_ASIA_HEAD;
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_WESTERN_CANADA_HEAD;
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_WESTERN_EUROPE_HEAD;
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_WESTERN_US_HEAD;

GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_CARIBBEAN_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_CENTRAL_AFRICA_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_CENTRAL_AMERICA_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_CENTRAL_ASIA_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_CENTRAL_US_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_EASTERN_AFRICA_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_EASTERN_ASIA_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_EASTERN_CANADA_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_EASTERN_EUROPE_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_EASTERN_US_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_NORTH_AFRICA_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_NORTHERN_EUROPE_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_OCEANIA_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_SOUTH_AMERICA_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_SOUTHEASTERN_ASIA_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_SOUTHERN_AFRICA_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_SOUTHERN_ASIA_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_SOUTHERN_EUROPE_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_SOUTHERN_US_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_WESTERN_AFRICA_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_WESTERN_ASIA_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_WESTERN_CANADA_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_WESTERN_EUROPE_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_WESTERN_US_HEAD;

-- Allow SELECT on table
GRANT SELECT ON TABLE HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES TO ROLE HRZN_CARIBBEAN_HEAD;
GRANT SELECT ON TABLE HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES TO ROLE HRZN_CENTRAL_AFRICA_HEAD;
GRANT SELECT ON TABLE HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES TO ROLE HRZN_CENTRAL_AMERICA_HEAD;
GRANT SELECT ON TABLE HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES TO ROLE HRZN_CENTRAL_ASIA_HEAD;
GRANT SELECT ON TABLE HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES TO ROLE HRZN_CENTRAL_US_HEAD;
GRANT SELECT ON TABLE HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES TO ROLE HRZN_EASTERN_AFRICA_HEAD;
GRANT SELECT ON TABLE HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES TO ROLE HRZN_EASTERN_ASIA_HEAD;
GRANT SELECT ON TABLE HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES TO ROLE HRZN_EASTERN_CANADA_HEAD;
GRANT SELECT ON TABLE HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES TO ROLE HRZN_EASTERN_EUROPE_HEAD;
GRANT SELECT ON TABLE HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES TO ROLE HRZN_EASTERN_US_HEAD;
GRANT SELECT ON TABLE HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES TO ROLE HRZN_NORTH_AFRICA_HEAD;
GRANT SELECT ON TABLE HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES TO ROLE HRZN_NORTHERN_EUROPE_HEAD;
GRANT SELECT ON TABLE HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES TO ROLE HRZN_OCEANIA_HEAD;
GRANT SELECT ON TABLE HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES TO ROLE HRZN_SOUTH_AMERICA_HEAD;
GRANT SELECT ON TABLE HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES TO ROLE HRZN_SOUTHEASTERN_ASIA_HEAD;
GRANT SELECT ON TABLE HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES TO ROLE HRZN_SOUTHERN_AFRICA_HEAD;
GRANT SELECT ON TABLE HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES TO ROLE HRZN_SOUTHERN_ASIA_HEAD;
GRANT SELECT ON TABLE HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES TO ROLE HRZN_SOUTHERN_EUROPE_HEAD;
GRANT SELECT ON TABLE HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES TO ROLE HRZN_SOUTHERN_US_HEAD;
GRANT SELECT ON TABLE HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES TO ROLE HRZN_WESTERN_AFRICA_HEAD;
GRANT SELECT ON TABLE HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES TO ROLE HRZN_WESTERN_ASIA_HEAD;
GRANT SELECT ON TABLE HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES TO ROLE HRZN_WESTERN_CANADA_HEAD;
GRANT SELECT ON TABLE HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES TO ROLE HRZN_WESTERN_EUROPE_HEAD;
GRANT SELECT ON TABLE HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES TO ROLE HRZN_WESTERN_US_HEAD;

-- ===========================================================
-- Step 3: Create Row Access Policy
-- ===========================================================
CREATE OR REPLACE ROW ACCESS POLICY store_region_policy
AS (region STRING) RETURNS BOOLEAN ->
    CASE
        WHEN CURRENT_ROLE() = 'HRZN_CARIBBEAN_HEAD'         THEN region = 'Caribbean'
        WHEN CURRENT_ROLE() = 'HRZN_CENTRAL_AFRICA_HEAD'    THEN region = 'Central Africa'
        WHEN CURRENT_ROLE() = 'HRZN_CENTRAL_AMERICA_HEAD'   THEN region = 'Central America'
        WHEN CURRENT_ROLE() = 'HRZN_CENTRAL_ASIA_HEAD'      THEN region = 'Central Asia'
        WHEN CURRENT_ROLE() = 'HRZN_CENTRAL_US_HEAD'        THEN region = 'Central US'
        WHEN CURRENT_ROLE() = 'HRZN_EASTERN_AFRICA_HEAD'    THEN region = 'Eastern Africa'
        WHEN CURRENT_ROLE() = 'HRZN_EASTERN_ASIA_HEAD'      THEN region = 'Eastern Asia'
        WHEN CURRENT_ROLE() = 'HRZN_EASTERN_CANADA_HEAD'    THEN region = 'Eastern Canada'
        WHEN CURRENT_ROLE() = 'HRZN_EASTERN_EUROPE_HEAD'    THEN region = 'Eastern Europe'
        WHEN CURRENT_ROLE() = 'HRZN_EASTERN_US_HEAD'        THEN region = 'Eastern US'
        WHEN CURRENT_ROLE() = 'HRZN_NORTH_AFRICA_HEAD'      THEN region = 'North Africa'
        WHEN CURRENT_ROLE() = 'HRZN_NORTHERN_EUROPE_HEAD'   THEN region = 'Northern Europe'
        WHEN CURRENT_ROLE() = 'HRZN_OCEANIA_HEAD'           THEN region = 'Oceania'
        WHEN CURRENT_ROLE() = 'HRZN_SOUTH_AMERICA_HEAD'     THEN region = 'South America'
        WHEN CURRENT_ROLE() = 'HRZN_SOUTHEASTERN_ASIA_HEAD' THEN region = 'Southeastern Asia'
        WHEN CURRENT_ROLE() = 'HRZN_SOUTHERN_AFRICA_HEAD'   THEN region = 'Southern Africa'
        WHEN CURRENT_ROLE() = 'HRZN_SOUTHERN_ASIA_HEAD'     THEN region = 'Southern Asia'
        WHEN CURRENT_ROLE() = 'HRZN_SOUTHERN_EUROPE_HEAD'   THEN region = 'Southern Europe'
        WHEN CURRENT_ROLE() = 'HRZN_SOUTHERN_US_HEAD'       THEN region = 'Southern US'
        WHEN CURRENT_ROLE() = 'HRZN_WESTERN_AFRICA_HEAD'    THEN region = 'Western Africa'
        WHEN CURRENT_ROLE() = 'HRZN_WESTERN_ASIA_HEAD'      THEN region = 'Western Asia'
        WHEN CURRENT_ROLE() = 'HRZN_WESTERN_CANADA_HEAD'    THEN region = 'Western Canada'
        WHEN CURRENT_ROLE() = 'HRZN_WESTERN_EUROPE_HEAD'    THEN region = 'Western Europe'
        WHEN CURRENT_ROLE() = 'HRZN_WESTERN_US_HEAD'        THEN region = 'Western US'
        ELSE FALSE
    END;

-- Attach policy
ALTER TABLE HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.STORE_LOCATION
ADD ROW ACCESS POLICY store_region_policy ON (region);

-- ===========================================================
-- Step 4: Check current user
-- ===========================================================
SELECT CURRENT_USER();

-- ===========================================================
-- Step 5: Grant roles to your users
-- (replace with real usernames from your Snowflake account)
-- ===========================================================
GRANT ROLE HRZN_CARIBBEAN_HEAD          TO USER caribbean_user;
GRANT ROLE HRZN_CENTRAL_AFRICA_HEAD     TO USER central_africa_user;
GRANT ROLE HRZN_CENTRAL_AMERICA_HEAD    TO USER central_america_user;
GRANT ROLE HRZN_CENTRAL_ASIA_HEAD       TO USER central_asia_user;
GRANT ROLE HRZN_CENTRAL_US_HEAD         TO USER central_us_user;
GRANT ROLE HRZN_EASTERN_AFRICA_HEAD     TO USER eastern_africa_user;
GRANT ROLE HRZN_EASTERN_ASIA_HEAD       TO USER eastern_asia_user;
GRANT ROLE HRZN_EASTERN_CANADA_HEAD     TO USER eastern_canada_user;
GRANT ROLE HRZN_EASTERN_EUROPE_HEAD     TO USER eastern_europe_user;
GRANT ROLE HRZN_EASTERN_US_HEAD         TO USER eastern_us_user;
GRANT ROLE HRZN_NORTH_AFRICA_HEAD       TO USER north_africa_user;
GRANT ROLE HRZN_NORTHERN_EUROPE_HEAD    TO USER northern_europe_user;
GRANT ROLE HRZN_OCEANIA_HEAD            TO USER oceania_user;
GRANT ROLE HRZN_SOUTH_AMERICA_HEAD      TO USER south_america_user;
GRANT ROLE HRZN_SOUTHEASTERN_ASIA_HEAD  TO USER southeastern_asia_user;
GRANT ROLE HRZN_SOUTHERN_AFRICA_HEAD    TO USER southern_africa_user;
GRANT ROLE HRZN_SOUTHERN_ASIA_HEAD      TO USER southern_asia_user;
GRANT ROLE HRZN_SOUTHERN_EUROPE_HEAD    TO USER southern_europe_user;
GRANT ROLE HRZN_SOUTHERN_US_HEAD        TO USER southern_us_user;
GRANT ROLE HRZN_WESTERN_AFRICA_HEAD     TO USER western_africa_user;
GRANT ROLE HRZN_WESTERN_ASIA_HEAD       TO USER western_asia_user;
GRANT ROLE HRZN_WESTERN_CANADA_HEAD     TO USER western_canada_user;
GRANT ROLE HRZN_WESTERN_EUROPE_HEAD     TO USER western_europe_user;
GRANT ROLE HRZN_WESTERN_US_HEAD         TO USER WEST;


CREATE USER WEST PASSWORD='Test@123' DEFAULT_ROLE=HRZN_WESTERN_US_HEAD;

GRANT ROLE HRZN_WESTERN_US_HEAD TO USER WEST ;

-- Test as ROLE_EAST
USE ROLE HRZN_WESTERN_US_HEAD;


CREATE ROLE IF NOT EXISTS HRZN_WESTERN_US_HEAD;
CREATE USER IF NOT EXISTS WEST 
  PASSWORD = 'Test@123'
  DEFAULT_ROLE = HRZN_WESTERN_US_HEAD
  MUST_CHANGE_PASSWORD = FALSE;
GRANT ROLE HRZN_WESTERN_US_HEAD TO USER WEST;


USE ROLE HRZN_WESTERN_US_HEAD;

SELECT CURRENT_USER(), CURRENT_ROLE();

-- Try accessing your table
SELECT * FROM HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES;


SHOW GRANTS TO USER WEST;
USE ROLE HRZN_WESTERN_US_HEAD;
GRANT ROLE HRZN_WESTERN_US_HEAD TO USER ACCOUNTADMIN;

-- ===============================
-- Grants for HRZN_WESTERN_US_HEAD
-- ===============================

GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_WESTERN_US_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_WESTERN_US_HEAD;
GRANT CREATE SCHEMA ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_WESTERN_US_HEAD;

GRANT USAGE ON ALL SCHEMAS IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_WESTERN_US_HEAD;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_WESTERN_US_HEAD;

GRANT SELECT ON ALL TABLES IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_WESTERN_US_HEAD;
GRANT SELECT ON ALL VIEWS IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_WESTERN_US_HEAD;

-- Warehouse access
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_WESTERN_US_HEAD;

use role HRZN_WESTERN_US_HEAD;
-----------------------------------------------------------------------------------------------

-- =========================================
-- Create all HRZN Regional Head Roles
-- and assign them to SYSADMIN
-- =========================================

USE ROLE SECURITYADMIN;

CREATE OR REPLACE ROLE HRZN_CARIBBEAN_HEAD;
CREATE OR REPLACE ROLE HRZN_CENTRAL_AFRICA_HEAD;
CREATE OR REPLACE ROLE HRZN_CENTRAL_AMERICA_HEAD;
CREATE OR REPLACE ROLE HRZN_CENTRAL_ASIA_HEAD;
CREATE OR REPLACE ROLE HRZN_CENTRAL_US_HEAD;
CREATE OR REPLACE ROLE HRZN_EASTERN_AFRICA_HEAD;
CREATE OR REPLACE ROLE HRZN_EASTERN_ASIA_HEAD;
CREATE OR REPLACE ROLE HRZN_EASTERN_CANADA_HEAD;
CREATE OR REPLACE ROLE HRZN_EASTERN_EUROPE_HEAD;
CREATE OR REPLACE ROLE HRZN_EASTERN_US_HEAD;
CREATE OR REPLACE ROLE HRZN_NORTH_AFRICA_HEAD;
CREATE OR REPLACE ROLE HRZN_NORTHERN_EUROPE_HEAD;
CREATE OR REPLACE ROLE HRZN_OCEANIA_HEAD;
CREATE OR REPLACE ROLE HRZN_SOUTH_AMERICA_HEAD;
CREATE OR REPLACE ROLE HRZN_SOUTHEASTERN_ASIA_HEAD;
CREATE OR REPLACE ROLE HRZN_SOUTHERN_AFRICA_HEAD;
CREATE OR REPLACE ROLE HRZN_SOUTHERN_ASIA_HEAD;
CREATE OR REPLACE ROLE HRZN_SOUTHERN_EUROPE_HEAD;
CREATE OR REPLACE ROLE HRZN_SOUTHERN_US_HEAD;
CREATE OR REPLACE ROLE HRZN_WESTERN_AFRICA_HEAD;
CREATE OR REPLACE ROLE HRZN_WESTERN_ASIA_HEAD;
CREATE OR REPLACE ROLE HRZN_WESTERN_CANADA_HEAD;
CREATE OR REPLACE ROLE HRZN_WESTERN_EUROPE_HEAD;
CREATE OR REPLACE ROLE HRZN_WESTERN_US_HEAD;

-- =========================================
-- Grant Regional Roles to SYSADMIN
-- =========================================

GRANT ROLE HRZN_CARIBBEAN_HEAD TO ROLE SYSADMIN;
GRANT ROLE HRZN_CENTRAL_AFRICA_HEAD TO ROLE SYSADMIN;
GRANT ROLE HRZN_CENTRAL_AMERICA_HEAD TO ROLE SYSADMIN;
GRANT ROLE HRZN_CENTRAL_ASIA_HEAD TO ROLE SYSADMIN;
GRANT ROLE HRZN_CENTRAL_US_HEAD TO ROLE SYSADMIN;
GRANT ROLE HRZN_EASTERN_AFRICA_HEAD TO ROLE SYSADMIN;
GRANT ROLE HRZN_EASTERN_ASIA_HEAD TO ROLE SYSADMIN;
GRANT ROLE HRZN_EASTERN_CANADA_HEAD TO ROLE SYSADMIN;
GRANT ROLE HRZN_EASTERN_EUROPE_HEAD TO ROLE SYSADMIN;
GRANT ROLE HRZN_EASTERN_US_HEAD TO ROLE SYSADMIN;
GRANT ROLE HRZN_NORTH_AFRICA_HEAD TO ROLE SYSADMIN;
GRANT ROLE HRZN_NORTHERN_EUROPE_HEAD TO ROLE SYSADMIN;
GRANT ROLE HRZN_OCEANIA_HEAD TO ROLE SYSADMIN;
GRANT ROLE HRZN_SOUTH_AMERICA_HEAD TO ROLE SYSADMIN;
GRANT ROLE HRZN_SOUTHEASTERN_ASIA_HEAD TO ROLE SYSADMIN;
GRANT ROLE HRZN_SOUTHERN_AFRICA_HEAD TO ROLE SYSADMIN;
GRANT ROLE HRZN_SOUTHERN_ASIA_HEAD TO ROLE SYSADMIN;
GRANT ROLE HRZN_SOUTHERN_EUROPE_HEAD TO ROLE SYSADMIN;
GRANT ROLE HRZN_SOUTHERN_US_HEAD TO ROLE SYSADMIN;
GRANT ROLE HRZN_WESTERN_AFRICA_HEAD TO ROLE SYSADMIN;
GRANT ROLE HRZN_WESTERN_ASIA_HEAD TO ROLE SYSADMIN;
GRANT ROLE HRZN_WESTERN_CANADA_HEAD TO ROLE SYSADMIN;
GRANT ROLE HRZN_WESTERN_EUROPE_HEAD TO ROLE SYSADMIN;
GRANT ROLE HRZN_WESTERN_US_HEAD TO ROLE SYSADMIN;

-- ===============================
-- CARIBBEAN
-- ===============================
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_CARIBBEAN_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_CARIBBEAN_HEAD;
GRANT CREATE SCHEMA ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_CARIBBEAN_HEAD;

GRANT USAGE ON ALL SCHEMAS IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_CARIBBEAN_HEAD;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_CARIBBEAN_HEAD;

GRANT SELECT ON ALL TABLES IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_CARIBBEAN_HEAD;
GRANT SELECT ON ALL VIEWS IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_CARIBBEAN_HEAD;

GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_CARIBBEAN_HEAD;


-- ===============================
-- CENTRAL AFRICA
-- ===============================
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_CENTRAL_AFRICA_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_CENTRAL_AFRICA_HEAD;
GRANT CREATE SCHEMA ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_CENTRAL_AFRICA_HEAD;

GRANT USAGE ON ALL SCHEMAS IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_CENTRAL_AFRICA_HEAD;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_CENTRAL_AFRICA_HEAD;

GRANT SELECT ON ALL TABLES IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_CENTRAL_AFRICA_HEAD;
GRANT SELECT ON ALL VIEWS IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_CENTRAL_AFRICA_HEAD;

GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_CENTRAL_AFRICA_HEAD;


-- ===============================
-- CENTRAL AMERICA
-- ===============================
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_CENTRAL_AMERICA_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_CENTRAL_AMERICA_HEAD;
GRANT CREATE SCHEMA ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_CENTRAL_AMERICA_HEAD;

GRANT USAGE ON ALL SCHEMAS IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_CENTRAL_AMERICA_HEAD;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_CENTRAL_AMERICA_HEAD;

GRANT SELECT ON ALL TABLES IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_CENTRAL_AMERICA_HEAD;
GRANT SELECT ON ALL VIEWS IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_CENTRAL_AMERICA_HEAD;

GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_CENTRAL_AMERICA_HEAD;


-- ===============================
-- CENTRAL ASIA
-- ===============================
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_CENTRAL_ASIA_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_CENTRAL_ASIA_HEAD;
GRANT CREATE SCHEMA ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_CENTRAL_ASIA_HEAD;

GRANT USAGE ON ALL SCHEMAS IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_CENTRAL_ASIA_HEAD;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_CENTRAL_ASIA_HEAD;

GRANT SELECT ON ALL TABLES IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_CENTRAL_ASIA_HEAD;
GRANT SELECT ON ALL VIEWS IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_CENTRAL_ASIA_HEAD;

GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_CENTRAL_ASIA_HEAD;


-- ===============================
-- CENTRAL US
-- ===============================
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_CENTRAL_US_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_CENTRAL_US_HEAD;
GRANT CREATE SCHEMA ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_CENTRAL_US_HEAD;

GRANT USAGE ON ALL SCHEMAS IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_CENTRAL_US_HEAD;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_CENTRAL_US_HEAD;

GRANT SELECT ON ALL TABLES IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_CENTRAL_US_HEAD;
GRANT SELECT ON ALL VIEWS IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_CENTRAL_US_HEAD;

GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_CENTRAL_US_HEAD;

-- =========================================================
-- Grants for HRZN_EASTERN_AFRICA_HEAD
-- =========================================================
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_EASTERN_AFRICA_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_EASTERN_AFRICA_HEAD;
GRANT CREATE SCHEMA ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_EASTERN_AFRICA_HEAD;

GRANT USAGE ON ALL SCHEMAS IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_EASTERN_AFRICA_HEAD;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_EASTERN_AFRICA_HEAD;

GRANT SELECT ON ALL TABLES IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_EASTERN_AFRICA_HEAD;
GRANT SELECT ON ALL VIEWS IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_EASTERN_AFRICA_HEAD;

GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_EASTERN_AFRICA_HEAD;


-- =========================================================
-- Grants for HRZN_EASTERN_ASIA_HEAD
-- =========================================================
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_EASTERN_ASIA_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_EASTERN_ASIA_HEAD;
GRANT CREATE SCHEMA ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_EASTERN_ASIA_HEAD;

GRANT USAGE ON ALL SCHEMAS IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_EASTERN_ASIA_HEAD;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_EASTERN_ASIA_HEAD;

GRANT SELECT ON ALL TABLES IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_EASTERN_ASIA_HEAD;
GRANT SELECT ON ALL VIEWS IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_EASTERN_ASIA_HEAD;

GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_EASTERN_ASIA_HEAD;


-- =========================================================
-- Grants for HRZN_EASTERN_CANADA_HEAD
-- =========================================================
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_EASTERN_CANADA_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_EASTERN_CANADA_HEAD;
GRANT CREATE SCHEMA ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_EASTERN_CANADA_HEAD;

GRANT USAGE ON ALL SCHEMAS IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_EASTERN_CANADA_HEAD;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_EASTERN_CANADA_HEAD;

GRANT SELECT ON ALL TABLES IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_EASTERN_CANADA_HEAD;
GRANT SELECT ON ALL VIEWS IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_EASTERN_CANADA_HEAD;

GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_EASTERN_CANADA_HEAD;


-- =========================================================
-- Grants for HRZN_EASTERN_EUROPE_HEAD
-- =========================================================
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_EASTERN_EUROPE_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_EASTERN_EUROPE_HEAD;
GRANT CREATE SCHEMA ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_EASTERN_EUROPE_HEAD;

GRANT USAGE ON ALL SCHEMAS IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_EASTERN_EUROPE_HEAD;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_EASTERN_EUROPE_HEAD;

GRANT SELECT ON ALL TABLES IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_EASTERN_EUROPE_HEAD;
GRANT SELECT ON ALL VIEWS IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_EASTERN_EUROPE_HEAD;

GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_EASTERN_EUROPE_HEAD;


-- =========================================================
-- Grants for HRZN_EASTERN_US_HEAD
-- =========================================================
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_EASTERN_US_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_EASTERN_US_HEAD;
GRANT CREATE SCHEMA ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_EASTERN_US_HEAD;

GRANT USAGE ON ALL SCHEMAS IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_EASTERN_US_HEAD;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_EASTERN_US_HEAD;

GRANT SELECT ON ALL TABLES IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_EASTERN_US_HEAD;
GRANT SELECT ON ALL VIEWS IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_EASTERN_US_HEAD;

GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_EASTERN_US_HEAD;

-- =========================================
-- Grants for HRZN_NORTH_AFRICA_HEAD
-- =========================================
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_NORTH_AFRICA_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_NORTH_AFRICA_HEAD;
GRANT CREATE SCHEMA ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_NORTH_AFRICA_HEAD;

GRANT USAGE ON ALL SCHEMAS IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_NORTH_AFRICA_HEAD;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_NORTH_AFRICA_HEAD;

GRANT SELECT ON ALL TABLES IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_NORTH_AFRICA_HEAD;
GRANT SELECT ON ALL VIEWS IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_NORTH_AFRICA_HEAD;

-- Warehouse access
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_NORTH_AFRICA_HEAD;


-- =========================================
-- Grants for HRZN_NORTHERN_EUROPE_HEAD
-- =========================================
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_NORTHERN_EUROPE_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_NORTHERN_EUROPE_HEAD;
GRANT CREATE SCHEMA ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_NORTHERN_EUROPE_HEAD;

GRANT USAGE ON ALL SCHEMAS IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_NORTHERN_EUROPE_HEAD;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_NORTHERN_EUROPE_HEAD;

GRANT SELECT ON ALL TABLES IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_NORTHERN_EUROPE_HEAD;
GRANT SELECT ON ALL VIEWS IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_NORTHERN_EUROPE_HEAD;

-- Warehouse access
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_NORTHERN_EUROPE_HEAD;


-- =========================================
-- Grants for HRZN_OCEANIA_HEAD
-- =========================================
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_OCEANIA_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_OCEANIA_HEAD;
GRANT CREATE SCHEMA ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_OCEANIA_HEAD;

GRANT USAGE ON ALL SCHEMAS IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_OCEANIA_HEAD;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_OCEANIA_HEAD;

GRANT SELECT ON ALL TABLES IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_OCEANIA_HEAD;
GRANT SELECT ON ALL VIEWS IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_OCEANIA_HEAD;

-- Warehouse access
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_OCEANIA_HEAD;


-- =========================================
-- Grants for HRZN_SOUTH_AMERICA_HEAD
-- =========================================
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_SOUTH_AMERICA_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_SOUTH_AMERICA_HEAD;
GRANT CREATE SCHEMA ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_SOUTH_AMERICA_HEAD;

GRANT USAGE ON ALL SCHEMAS IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_SOUTH_AMERICA_HEAD;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_SOUTH_AMERICA_HEAD;

GRANT SELECT ON ALL TABLES IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_SOUTH_AMERICA_HEAD;
GRANT SELECT ON ALL VIEWS IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_SOUTH_AMERICA_HEAD;

-- Warehouse access
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_SOUTH_AMERICA_HEAD;


-- =========================================
-- Grants for HRZN_SOUTHEASTERN_ASIA_HEAD
-- =========================================
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_SOUTHEASTERN_ASIA_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_SOUTHEASTERN_ASIA_HEAD;
GRANT CREATE SCHEMA ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_SOUTHEASTERN_ASIA_HEAD;

GRANT USAGE ON ALL SCHEMAS IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_SOUTHEASTERN_ASIA_HEAD;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_SOUTHEASTERN_ASIA_HEAD;

GRANT SELECT ON ALL TABLES IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_SOUTHEASTERN_ASIA_HEAD;
GRANT SELECT ON ALL VIEWS IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_SOUTHEASTERN_ASIA_HEAD;

-- Warehouse access
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_SOUTHEASTERN_ASIA_HEAD;


-- =====================================================================
-- Grants for HRZN_SOUTHERN_AFRICA_HEAD
-- =====================================================================
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_SOUTHERN_AFRICA_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_SOUTHERN_AFRICA_HEAD;
GRANT CREATE SCHEMA ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_SOUTHERN_AFRICA_HEAD;

GRANT USAGE ON ALL SCHEMAS IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_SOUTHERN_AFRICA_HEAD;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_SOUTHERN_AFRICA_HEAD;

GRANT SELECT ON ALL TABLES IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_SOUTHERN_AFRICA_HEAD;
GRANT SELECT ON ALL VIEWS IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_SOUTHERN_AFRICA_HEAD;

-- Warehouse access
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_SOUTHERN_AFRICA_HEAD;


-- =====================================================================
-- Grants for HRZN_SOUTHERN_ASIA_HEAD
-- =====================================================================
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_SOUTHERN_ASIA_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_SOUTHERN_ASIA_HEAD;
GRANT CREATE SCHEMA ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_SOUTHERN_ASIA_HEAD;

GRANT USAGE ON ALL SCHEMAS IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_SOUTHERN_ASIA_HEAD;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_SOUTHERN_ASIA_HEAD;

GRANT SELECT ON ALL TABLES IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_SOUTHERN_ASIA_HEAD;
GRANT SELECT ON ALL VIEWS IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_SOUTHERN_ASIA_HEAD;

-- Warehouse access
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_SOUTHERN_ASIA_HEAD;


-- =====================================================================
-- Grants for HRZN_SOUTHERN_EUROPE_HEAD
-- =====================================================================
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_SOUTHERN_EUROPE_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_SOUTHERN_EUROPE_HEAD;
GRANT CREATE SCHEMA ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_SOUTHERN_EUROPE_HEAD;

GRANT USAGE ON ALL SCHEMAS IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_SOUTHERN_EUROPE_HEAD;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_SOUTHERN_EUROPE_HEAD;

GRANT SELECT ON ALL TABLES IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_SOUTHERN_EUROPE_HEAD;
GRANT SELECT ON ALL VIEWS IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_SOUTHERN_EUROPE_HEAD;

-- Warehouse access
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_SOUTHERN_EUROPE_HEAD;


-- =====================================================================
-- Grants for HRZN_SOUTHERN_US_HEAD
-- =====================================================================
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_SOUTHERN_US_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_SOUTHERN_US_HEAD;
GRANT CREATE SCHEMA ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_SOUTHERN_US_HEAD;

GRANT USAGE ON ALL SCHEMAS IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_SOUTHERN_US_HEAD;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_SOUTHERN_US_HEAD;

GRANT SELECT ON ALL TABLES IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_SOUTHERN_US_HEAD;
GRANT SELECT ON ALL VIEWS IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_SOUTHERN_US_HEAD;

-- Warehouse access
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_SOUTHERN_US_HEAD;


-- =====================================================================
-- Grants for HRZN_WESTERN_AFRICA_HEAD
-- =====================================================================
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_WESTERN_AFRICA_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_WESTERN_AFRICA_HEAD;
GRANT CREATE SCHEMA ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_WESTERN_AFRICA_HEAD;

GRANT USAGE ON ALL SCHEMAS IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_WESTERN_AFRICA_HEAD;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_WESTERN_AFRICA_HEAD;

GRANT SELECT ON ALL TABLES IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_WESTERN_AFRICA_HEAD;
GRANT SELECT ON ALL VIEWS IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_WESTERN_AFRICA_HEAD;

-- Warehouse access
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_WESTERN_AFRICA_HEAD;


-- =====================================================================
-- Grants for HRZN_WESTERN_ASIA_HEAD
-- =====================================================================
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_WESTERN_ASIA_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_WESTERN_ASIA_HEAD;
GRANT CREATE SCHEMA ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_WESTERN_ASIA_HEAD;

GRANT USAGE ON ALL SCHEMAS IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_WESTERN_ASIA_HEAD;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_WESTERN_ASIA_HEAD;

GRANT SELECT ON ALL TABLES IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_WESTERN_ASIA_HEAD;
GRANT SELECT ON ALL VIEWS IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_WESTERN_ASIA_HEAD;

-- Warehouse access
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_WESTERN_ASIA_HEAD;


-- =====================================================================
-- Grants for HRZN_WESTERN_CANADA_HEAD
-- =====================================================================
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_WESTERN_CANADA_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_WESTERN_CANADA_HEAD;
GRANT CREATE SCHEMA ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_WESTERN_CANADA_HEAD;

GRANT USAGE ON ALL SCHEMAS IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_WESTERN_CANADA_HEAD;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_WESTERN_CANADA_HEAD;

GRANT SELECT ON ALL TABLES IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_WESTERN_CANADA_HEAD;
GRANT SELECT ON ALL VIEWS IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_WESTERN_CANADA_HEAD;

-- Warehouse access
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_WESTERN_CANADA_HEAD;


-- =====================================================================
-- Grants for HRZN_WESTERN_EUROPE_HEAD
-- =====================================================================
GRANT USAGE ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_WESTERN_EUROPE_HEAD;
GRANT USAGE ON SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_WESTERN_EUROPE_HEAD;
GRANT CREATE SCHEMA ON DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_WESTERN_EUROPE_HEAD;

GRANT USAGE ON ALL SCHEMAS IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_WESTERN_EUROPE_HEAD;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN DATABASE HRZN_GLOBAL_DB TO ROLE HRZN_WESTERN_EUROPE_HEAD;

GRANT SELECT ON ALL TABLES IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_WESTERN_EUROPE_HEAD;
GRANT SELECT ON ALL VIEWS IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH TO ROLE HRZN_WESTERN_EUROPE_HEAD;

-- Warehouse access
GRANT USAGE ON WAREHOUSE HRZN_WH TO ROLE HRZN_WESTERN_EUROPE_HEAD;



use role HRZN_WESTERN_EUROPE_HEAD;
SELECT * FROM HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.RE;