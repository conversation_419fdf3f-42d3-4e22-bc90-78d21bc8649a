# 🔧 Troubleshooting Guide - Role-Based Chatbot

## 🚨 Common Issues and Solutions

### Issue 1: Role Switching Error
**Error:** `❌ Failed to switch to role HRZN_WESTERN_US_HEAD: Unsupported statement type 'USE'`

**Root Cause:** Streamlit in Snowflake doesn't support dynamic role switching using `USE ROLE` statements.

**Solutions:**

#### Option A: Pre-deploy with Specific Role (Recommended)
1. Deploy separate instances of the app for each role
2. Each instance runs under a specific role from the start
3. No dynamic switching needed

```sql
-- Deploy app as HRZN_WESTERN_US_HEAD
USE ROLE HRZN_WESTERN_US_HEAD;
-- Then deploy your Streamlit app
```

#### Option B: Use Role-Based App Access
1. Create different Streamlit apps for different roles
2. Grant access to each app based on user roles
3. Users access the app that matches their role

#### Option C: Manual Role Management
1. Users must manually switch roles in Snowflake before using the app
2. Remove role switching from the app
3. Display current role for reference only

### Issue 2: "Query returned no data"
**Possible Causes:**

#### A. Semantic Model File Mismatch
- **Check:** Your YAML file is named `GLOBAL_SALES_FINAL 8_21_2025, 3_36 PM.yaml`
- **But app looks for:** `global_sales.yaml` or `GLOBAL_SALES_FINAL.yaml`

**Solution:**
1. Rename your YAML file to `GLOBAL_SALES_FINAL.yaml`
2. Upload it to the correct stage: `@TEST_DB.PUBLIC.MY_STAGE/`
3. Or update the app to use the correct filename

#### B. Database/Schema Access Issues
**Check these:**
1. Does `HRZN_GLOBAL_DB` database exist?
2. Does `HRZN_GLOBAL_SCH` schema exist?
3. Do the tables `SALES`, `RETURN`, `REPRESENTATIVES` exist?
4. Does your current role have access to these objects?

**Solution:**
```sql
-- Check database
SHOW DATABASES LIKE 'HRZN_GLOBAL_DB';

-- Check schema
SHOW SCHEMAS LIKE 'HRZN_GLOBAL_SCH' IN DATABASE HRZN_GLOBAL_DB;

-- Check tables
SHOW TABLES IN SCHEMA HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH;

-- Test data access
SELECT COUNT(*) FROM HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES;
```

#### C. Row Access Policy Issues
**Check:**
1. Are row access policies applied to the tables?
2. Is your current role included in the policy?
3. Are there any data records for your role's region?

**Solution:**
```sql
-- Check policies
SHOW ROW ACCESS POLICIES;

-- Check current role
SELECT CURRENT_ROLE();

-- Test without policy (as admin)
USE ROLE ACCOUNTADMIN;
SELECT REGION, COUNT(*) FROM HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES GROUP BY REGION;
```

## 🛠️ Quick Fixes

### Fix 1: Update Semantic Model Path
Update your app.py to match your actual YAML file:

```python
AVAILABLE_SEMANTIC_MODELS_PATHS = [
    '@"TEST_DB"."PUBLIC"."MY_STAGE"/GLOBAL_SALES_FINAL.yaml',
    # Add other models as needed
]
```

### Fix 2: Simplify Role Management
Remove dynamic role switching and use display-only role selection:

```python
def switch_role(role_name: str):
    """Display-only role selection - no actual switching."""
    st.session_state.selected_role = role_name
    st.info(f"🔐 Role selected: {AVAILABLE_ROLES[role_name]} ({role_name})")
    st.warning("⚠️ Note: Please manually switch to this role in Snowflake for data filtering to work.")
    return True
```

### Fix 3: Test Data Access
Use the debug buttons in your app:
1. Click "🔍 Debug Data Access" to check all components
2. Click "🧪 Test Current Role" to verify data access
3. Review the output to identify specific issues

## 📋 Deployment Checklist

### Before Deploying:
- [ ] YAML file uploaded to correct stage with correct name
- [ ] Database `HRZN_GLOBAL_DB` exists and is accessible
- [ ] Schema `HRZN_GLOBAL_SCH` exists and is accessible
- [ ] Tables `SALES`, `RETURN`, `REPRESENTATIVES` exist with data
- [ ] Row access policies are applied and working
- [ ] Regional roles exist and have proper permissions
- [ ] Test queries work manually in Snowflake

### After Deploying:
- [ ] App loads without errors
- [ ] Debug functions show all green checkmarks
- [ ] Test queries return data in "Full Access" mode
- [ ] Role selection works (even if just for display)
- [ ] Cortex Analyst responds to questions

## 🔍 Testing Steps

### Step 1: Manual Testing in Snowflake
```sql
-- Test as admin
USE ROLE ACCOUNTADMIN;
SELECT * FROM HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES LIMIT 5;

-- Test with regional role
USE ROLE HRZN_WESTERN_US_HEAD;
SELECT REGION, COUNT(*) FROM HRZN_GLOBAL_DB.HRZN_GLOBAL_SCH.SALES GROUP BY REGION;
```

### Step 2: Test Semantic Model
```sql
-- Check stage contents
LIST @TEST_DB.PUBLIC.MY_STAGE;

-- Verify YAML file is accessible
SELECT $1 FROM @TEST_DB.PUBLIC.MY_STAGE/GLOBAL_SALES_FINAL.yaml LIMIT 1;
```

### Step 3: Test in App
1. Deploy the app
2. Use "Debug Data Access" button
3. Try simple questions like "Show me sales data"
4. Check if Cortex Analyst responds

## 💡 Recommended Approach

For immediate success, I recommend:

1. **Simplify first:** Remove dynamic role switching
2. **Test basic functionality:** Ensure data access works in "Full Access" mode
3. **Fix semantic model:** Ensure YAML file is correctly named and uploaded
4. **Test manually:** Verify row access policies work in Snowflake directly
5. **Add role features:** Once basic functionality works, enhance with role features

This step-by-step approach will help you identify and fix issues systematically.
