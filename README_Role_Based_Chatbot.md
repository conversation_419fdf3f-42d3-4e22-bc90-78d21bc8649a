# ❄️ SalesBot - Role-Based Chatbot

## Overview
This Streamlit chatbot application provides role-based access to sales data using Snowflake's Row Access Policies. Users can select different regional roles to view filtered data specific to their region.

## 🔐 Role-Based Access Control

### How It Works
1. **Row Access Policies**: Implemented at the database level in Snowflake
2. **Regional Roles**: Each role corresponds to a specific geographic region
3. **Automatic Filtering**: When a role is selected, all queries automatically filter data for that region only
4. **Secure Access**: Data filtering is enforced by <PERSON>flake, not just the application

### Available Roles
The following regional head roles are available:

- **Caribbean**: HRZN_CARIBBEAN_HEAD
- **Central Africa**: HRZN_CENTRAL_AFRICA_HEAD  
- **Central America**: HRZN_CENTRAL_AMERICA_HEAD
- **Central Asia**: HRZN_CENTRAL_ASIA_HEAD
- **Central US**: HRZN_CENTRAL_US_HEAD
- **Eastern Africa**: HRZN_EASTERN_AFRICA_HEAD
- **Eastern Asia**: HRZN_EASTERN_ASIA_HEAD
- **Eastern Canada**: HRZN_EASTERN_CANADA_HEAD
- **North Africa**: HRZN_NORTH_AFRICA_HEAD
- **Northern Asia**: HRZN_NORTHERN_ASIA_HEAD
- **Northern Europe**: HRZN_NORTHERN_EUROPE_HEAD
- **Oceania**: HRZN_OCEANIA_HEAD
- **Southeastern Asia**: HRZN_SOUTHEASTERN_ASIA_HEAD
- **Southern Africa**: HRZN_SOUTHERN_AFRICA_HEAD
- **Southern Asia**: HRZN_SOUTHERN_ASIA_HEAD
- **Southern Europe**: HRZN_SOUTHERN_EUROPE_HEAD
- **Southern US**: HRZN_SOUTHERN_US_HEAD
- **Western Africa**: HRZN_WESTERN_AFRICA_HEAD
- **Western Asia**: HRZN_WESTERN_ASIA_HEAD
- **Western Canada**: HRZN_WESTERN_CANADA_HEAD
- **Western Europe**: HRZN_WESTERN_EUROPE_HEAD
- **Western US**: HRZN_WESTERN_US_HEAD

## 🚀 How to Use

### 1. Select Your Role
- In the sidebar, use the "Select your role" dropdown
- Choose your regional role or "None (Full Access)" for all data
- The app will automatically switch to that role in Snowflake

### 2. Ask Questions
Try these sample questions to see role-based filtering in action:

**Sales Analysis:**
- "Show me total sales by region"
- "What are the top 10 products by sales?"
- "Show sales trends over time"

**Regional Insights:**
- "How many customers do we have?"
- "What's the average order value?"
- "Show me sales by category"

**Performance Metrics:**
- "What's our profit margin by product category?"
- "Show me return rates"
- "Which shipping mode is most popular?"

### 3. Test Your Role
- Click the "🧪 Test Current Role" button to verify your access
- This will show which regions you can access with your current role

## 📊 Features

### Visual Indicators
- **Header Status**: Shows your current role and access level
- **Query Results**: Displays which region's data is being shown
- **Data Count**: Shows how many records were found for your region
- **Helpful Messages**: Explains when no data is found for your region

### Data Tables Covered
The row access policies are applied to these tables:
- **SALES**: Main sales transaction data
- **RETURN**: Product return information  
- **REPRESENTATIVES**: Sales representative data

All tables are filtered by the `REGION` column based on your selected role.

## 🛠️ Technical Implementation

### Row Access Policy
```sql
CREATE OR REPLACE ROW ACCESS POLICY store_region_policy
AS (region STRING) RETURNS BOOLEAN ->
    CASE
        WHEN CURRENT_ROLE() = 'HRZN_CARIBBEAN_HEAD' THEN region = 'Caribbean'
        WHEN CURRENT_ROLE() = 'HRZN_CENTRAL_US_HEAD' THEN region = 'Central US'
        -- ... (other roles)
        ELSE FALSE
    END;
```

### Role Switching in Python
```python
def switch_role(role_name: str):
    session.sql(f"USE ROLE {role_name}").collect()
    st.session_state.selected_role = role_name
```

## 🔍 Troubleshooting

### No Data Returned
If you get no results for your region:
1. Check if there's data for your region in the selected time period
2. Verify your query filters aren't too restrictive
3. Try switching to "Full Access" mode to see all data
4. Use the "Test Current Role" feature to verify your access

### Role Switch Errors
If role switching fails:
1. Ensure the role exists in Snowflake
2. Check that your user has been granted the role
3. Verify the role has proper permissions on the database/schema

## 📝 Notes
- Chat history is reset when switching roles to avoid confusion
- All data filtering happens at the database level for security
- The semantic model (YAML file) remains the same regardless of role
- Cortex Analyst generates queries that are automatically filtered by the row access policy
